/**
 * 优化的业务数据管理Hook
 * 🎯 核心价值：统一业务数据管理，避免重复渲染
 * 📦 功能：数据获取、状态管理、查询、验证、统计
 * ⚡ 性能优化：使用reducer模式，稳定的函数引用
 */

import { useReducer, useCallback, useRef, useEffect, useMemo } from 'react';

// 业务数据类型
export type BusinessDataType = 'cell' | 'group' | 'color' | 'style';

// 业务数据项基础接口
export interface BusinessDataItem {
  active?: boolean;
  visible?: boolean;
  type?: string;
  level?: number;
  [key: string]: any;
}

// 类型保护函数
export function isBusinessDataItem(item: unknown): item is BusinessDataItem {
  return typeof item === 'object' && item !== null;
}

// 统计计算工具函数 - 提取重复逻辑
function createBusinessStats<T>(data: T): BusinessStats {
  if (!data || !Array.isArray(data)) {
    return {
      total: 0,
      active: 0,
      visible: 0,
      byType: {},
      byLevel: {},
      lastUpdate: Date.now(),
    };
  }

  const stats: BusinessStats = {
    total: data.length,
    active: 0,
    visible: 0,
    byType: {},
    byLevel: {},
    lastUpdate: Date.now(),
  };

  data.forEach((item: unknown) => {
    if (!isBusinessDataItem(item)) return;

    if (item.active) stats.active++;
    if (item.visible) stats.visible++;

    if (item.type) {
      stats.byType[item.type] = (stats.byType[item.type] || 0) + 1;
    }

    if (item.level) {
      stats.byLevel[item.level] = (stats.byLevel[item.level] || 0) + 1;
    }
  });

  return stats;
}

// 缓存管理工具函数
function createCacheManager<T>(cacheRef: React.MutableRefObject<Map<string, CacheEntry<T>>>) {
  return {
    get: (key: string, timeout: number): T | null => {
      const cached = cacheRef.current.get(key);
      if (!cached) return null;

      const now = Date.now();
      if (now - cached.timestamp > timeout) {
        cacheRef.current.delete(key);
        return null;
      }

      return cached.data;
    },

    set: (key: string, data: T): void => {
      cacheRef.current.set(key, {
        data,
        timestamp: Date.now()
      });
    },

    clear: (): void => {
      cacheRef.current.clear();
    }
  };
}

// 查询条件
export interface QueryConditions {
  color?: string;
  level?: number;
  group?: number;
  active?: boolean;
  visible?: boolean;
  [key: string]: any;
}

// 统计信息
export interface BusinessStats {
  total: number;
  active: number;
  visible: number;
  byType: Record<string, number>;
  byLevel: Record<number, number>;
  lastUpdate: number;
}

// 配置接口
export interface BusinessDataConfig<T> {
  dataType: BusinessDataType;
  fetcher: () => Promise<T>;
  validator?: (data: T) => string | null;
  transformer?: (data: unknown) => T;
  enableCache?: boolean;
  cacheTimeout?: number; // 毫秒
  enableAutoRefresh?: boolean;
  refreshInterval?: number; // 毫秒
  fetchOnMount?: boolean;
}

// 缓存条目类型
interface CacheEntry<T> {
  data: T;
  timestamp: number;
}

// 状态接口
interface BusinessDataState<T> {
  data: T | null;
  isLoading: boolean;
  error: string | null;
  isRefreshing: boolean;
  lastFetchTime: number | null;
  stats: BusinessStats | null;
}

// Action类型
type BusinessDataAction<T> =
  | { type: 'FETCH_START' }
  | { type: 'FETCH_SUCCESS'; payload: T }
  | { type: 'FETCH_ERROR'; payload: string }
  | { type: 'REFRESH_START' }
  | { type: 'REFRESH_SUCCESS'; payload: T }
  | { type: 'RESET' }
  | { type: 'UPDATE_STATS'; payload: BusinessStats };

// Reducer函数
function businessDataReducer<T>(
  state: BusinessDataState<T>, 
  action: BusinessDataAction<T>
): BusinessDataState<T> {
  switch (action.type) {
    case 'FETCH_START':
      return {
        ...state,
        isLoading: true,
        error: null,
      };
    
    case 'FETCH_SUCCESS':
      return {
        ...state,
        data: action.payload,
        isLoading: false,
        error: null,
        lastFetchTime: Date.now(),
      };
    
    case 'FETCH_ERROR':
      return {
        ...state,
        isLoading: false,
        error: action.payload,
      };
    
    case 'REFRESH_START':
      return {
        ...state,
        isRefreshing: true,
        error: null,
      };
    
    case 'REFRESH_SUCCESS':
      return {
        ...state,
        data: action.payload,
        isRefreshing: false,
        error: null,
        lastFetchTime: Date.now(),
      };
    

    
    case 'UPDATE_STATS':
      return {
        ...state,
        stats: action.payload,
      };
    
    case 'RESET':
      return {
        data: null,
        isLoading: false,
        error: null,
        isRefreshing: false,
        lastFetchTime: null,
        stats: null,
      };
    
    default:
      return state;
  }
}

// Hook返回类型
export interface UseBusinessDataReturn<T> {
  // 状态
  data: T | null;
  isLoading: boolean;
  error: string | null;
  isRefreshing: boolean;
  lastFetchTime: number | null;
  stats: BusinessStats | null;
  
  // 操作
  fetch: () => Promise<void>;
  refresh: () => Promise<void>;
  reset: () => void;
  
  // 查询功能
  query: (conditions: QueryConditions) => any[];
  filter: (predicate: (item: unknown) => boolean) => any[];
  search: (searchTerm: string, fields: string[]) => any[];
  
  // 统计功能
  getStats: () => BusinessStats;
  getCount: (conditions?: QueryConditions) => number;
  
  // 验证功能
  validate: () => boolean;
  
  // 缓存控制
  clearCache: () => void;
  getCacheInfo: () => { size: number; lastUpdate: number };
}

export function useBusinessData<T>(
  config: BusinessDataConfig<T>
): UseBusinessDataReturn<T> {
  const {
    dataType,
    validator,
    transformer,
    enableAutoRefresh = false,
    refreshInterval = 30000,
    fetchOnMount = true,
  } = config;

  // 初始状态
  const initialState: BusinessDataState<T> = {
    data: null,
    isLoading: false,
    error: null,
    isRefreshing: false,
    lastFetchTime: null,
    stats: null,
  };

  const [state, dispatch] = useReducer(businessDataReducer<T>, initialState);

  // 稳定的引用
  const configRef = useRef(config);
  const abortControllerRef = useRef<AbortController | null>(null);
  const refreshTimerRef = useRef<NodeJS.Timeout | null>(null);
  const cacheRef = useRef<Map<string, CacheEntry<T>>>(new Map());
  
  // 更新配置引用
  configRef.current = config;

  // 缓存键
  const cacheKey = `business_${dataType}`;

  // 缓存管理器
  const cacheManager = useMemo(() => createCacheManager(cacheRef), []);

  // 数据转换
  const transformedData = useMemo(() => {
    if (!state.data) return null;
    return transformer ? transformer(state.data) : state.data;
  }, [state.data, transformer]);

  // 计算统计信息 - 提取为独立的工具函数
  const calculateStats = useCallback((data: T): BusinessStats => {
    return createBusinessStats(data);
  }, []);

  // 核心数据获取函数 - 重构后简化逻辑
  const fetchData = useCallback(async (isRefresh = false) => {
    // 取消之前的请求
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    abortControllerRef.current = new AbortController();

    dispatch({ type: isRefresh ? 'REFRESH_START' : 'FETCH_START' });

    try {
      // 检查缓存（非刷新时）
      if (!isRefresh && configRef.current.enableCache) {
        const cachedData = cacheManager.get(cacheKey, configRef.current.cacheTimeout || 5 * 60 * 1000);
        if (cachedData) {
          dispatch({
            type: 'FETCH_SUCCESS',
            payload: cachedData
          });
          return;
        }
      }

      // 获取数据
      const data = await configRef.current.fetcher();

      // 检查是否被取消
      if (abortControllerRef.current?.signal.aborted) {
        return;
      }

      // 更新状态
      dispatch({
        type: isRefresh ? 'REFRESH_SUCCESS' : 'FETCH_SUCCESS',
        payload: data
      });

      // 更新缓存
      if (configRef.current.enableCache) {
        cacheManager.set(cacheKey, data);
      }

      // 计算并更新统计信息
      const stats = createBusinessStats(data);
      dispatch({ type: 'UPDATE_STATS', payload: stats });

    } catch (error) {
      if (abortControllerRef.current?.signal.aborted) {
        return;
      }

      const errorMessage = error instanceof Error ? error.message : '数据获取失败';
      dispatch({ type: 'FETCH_ERROR', payload: errorMessage });
    }
  }, [cacheKey, cacheManager]); // 依赖稳定的引用

  // 存储fetchData的稳定引用
  const fetchDataRef = useRef(fetchData);
  fetchDataRef.current = fetchData;

  // 公共操作函数 - 使用稳定的引用
  const fetch = useCallback(async () => {
    if (state.isLoading) return;
    await fetchDataRef.current(false);
  }, [state.isLoading]);

  const refresh = useCallback(async () => {
    if (state.isRefreshing) return;
    await fetchDataRef.current(true);
  }, [state.isRefreshing]);

  const reset = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    if (refreshTimerRef.current) {
      clearInterval(refreshTimerRef.current);
    }

    dispatch({ type: 'RESET' });
  }, []);

  // 查询功能
  const query = useCallback((conditions: QueryConditions) => {
    if (!transformedData || !Array.isArray(transformedData)) return [];

    return transformedData.filter((item: unknown) => {
      if (!isBusinessDataItem(item)) return false;

      return Object.entries(conditions).every(([key, value]) => {
        if (value === undefined) return true;
        return item[key] === value;
      });
    });
  }, [transformedData]);

  const filter = useCallback((predicate: (item: unknown) => boolean) => {
    if (!transformedData || !Array.isArray(transformedData)) return [];
    return transformedData.filter(predicate);
  }, [transformedData]);

  const search = useCallback((searchTerm: string, fields: string[]) => {
    if (!transformedData || !Array.isArray(transformedData)) return [];
    if (!searchTerm.trim()) return transformedData;

    const term = searchTerm.toLowerCase();
    return transformedData.filter((item: unknown) => {
      if (!isBusinessDataItem(item)) return false;

      return fields.some(field => {
        const value = item[field];
        return value && value.toString().toLowerCase().includes(term);
      });
    });
  }, [transformedData]);

  // 统计功能
  const getStats = useCallback((): BusinessStats => {
    if (state.stats) return state.stats;

    if (!transformedData) {
      return {
        total: 0,
        active: 0,
        visible: 0,
        byType: {},
        byLevel: {},
        lastUpdate: Date.now(),
      };
    }

    return calculateStats(transformedData);
  }, [state.stats, transformedData, calculateStats]);

  const getCount = useCallback((conditions?: QueryConditions) => {
    if (!conditions) {
      return transformedData ? (Array.isArray(transformedData) ? transformedData.length : 1) : 0;
    }
    return query(conditions).length;
  }, [transformedData, query]);

  // 验证功能
  const validate = useCallback((): boolean => {
    if (!transformedData || !validator) return true;
    const error = validator(transformedData);
    return !error;
  }, [transformedData, validator]);

  // 缓存控制
  const clearCache = useCallback(() => {
    cacheManager.clear();
    dispatch({ type: 'RESET' });
  }, [cacheManager]);

  const getCacheInfo = useCallback(() => {
    return {
      size: transformedData ? JSON.stringify(transformedData).length : 0,
      lastUpdate: state.lastFetchTime || 0,
    };
  }, [transformedData, state.lastFetchTime]);

  // 自动刷新效果
  useEffect(() => {
    if (!enableAutoRefresh || !refreshInterval) return;

    refreshTimerRef.current = setInterval(() => {
      fetchDataRef.current(true);
    }, refreshInterval);

    return () => {
      if (refreshTimerRef.current) {
        clearInterval(refreshTimerRef.current);
      }
    };
  }, [enableAutoRefresh, refreshInterval]);

  // 初始化获取数据 - 使用ref避免依赖state变化
  const hasInitializedRef = useRef(false);

  useEffect(() => {
    if (fetchOnMount && !hasInitializedRef.current && !state.data && !state.isLoading) {
      hasInitializedRef.current = true;
      fetchDataRef.current(false);
    }
  }, [fetchOnMount]); // 只依赖fetchOnMount，避免state变化导致重复执行

  // 清理函数单独处理
  useEffect(() => {
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
      if (refreshTimerRef.current) {
        clearInterval(refreshTimerRef.current);
      }
    };
  }, []); // 空依赖数组，只在组件卸载时执行

  return {
    // 状态
    data: transformedData,
    isLoading: state.isLoading,
    error: state.error,
    isRefreshing: state.isRefreshing,
    lastFetchTime: state.lastFetchTime,
    stats: state.stats,

    // 操作
    fetch,
    refresh,
    reset,

    // 查询功能
    query,
    filter,
    search,

    // 统计功能
    getStats,
    getCount,

    // 验证功能
    validate,

    // 缓存控制
    clearCache,
    getCacheInfo,
  };
}
